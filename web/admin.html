<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络管理控制台</title>
    <!-- 引入加密库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script src="https://unpkg.com/fernet@0.3.3/fernetBrowser.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 0;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar h2 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 1.5em;
            padding: 0 20px;
        }

        .nav-item {
            display: block;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            transition: background 0.3s;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.1);
        }

        .nav-item i {
            margin-right: 10px;
            width: 16px;
        }

        .main-content {
            margin-left: 250px;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: white;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 1.8em;
        }

        .connection-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e74c3c;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #27ae60;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }

        .card-body {
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            opacity: 0.9;
            font-size: 0.9em;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        .btn-danger { background: #e74c3c; }
        .btn-danger:hover { background: #c0392b; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 200px;
            font-family: 'Courier New', monospace;
            resize: vertical;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .endpoint-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .endpoint-info {
            flex: 1;
        }

        .endpoint-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .endpoint-details {
            font-size: 12px;
            color: #666;
        }

        .endpoint-actions {
            display: flex;
            gap: 5px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .header {
                padding: 15px 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 表单配置样式 */
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            font-size: 14px;
        }

        .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-group input[type="number"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input[readonly] {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }

        .form-group small {
            color: #6c757d;
            font-size: 12px;
        }

        .form-group label input[type="checkbox"] {
            margin-right: 8px;
        }

        #connectionStatus {
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }

        .protocol-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 8px;
        }

        .protocol-http {
            background-color: #ffc107;
            color: #212529;
        }


    </style>
</head>
<body>
    <div class="sidebar">
        <h2>🚀 网络管理</h2>
        <button class="nav-item active" onclick="showTab('dashboard')">
            <span>📊 仪表板</span>
        </button>
        <button class="nav-item" onclick="showTab('endpoints')">
            <span>🔗 端点管理</span>
        </button>
        <button class="nav-item" onclick="showTab('config')">
            <span>⚙️ 配置管理</span>
        </button>
        <button class="nav-item" onclick="showTab('traffic')">
            <span>📈 流量统计</span>
        </button>
        <button class="nav-item" onclick="showTab('connections')">
            <span>🌐 连接管理</span>
        </button>
        <button class="nav-item" onclick="showTab('logs')">
            <span>📝 日志查看</span>
        </button>
        <button class="nav-item" onclick="showTab('settings')">
            <span>🔧 系统设置</span>
        </button>
    </div>

    <div class="main-content">
        <div class="header">
            <h1 id="pageTitle">仪表板</h1>
            <div class="connection-info">
                <div class="status-dot" id="connectionDot"></div>
                <span id="connectionText">未连接</span>
                <button class="btn btn-success" onclick="testConnection()">测试连接</button>
            </div>
        </div>

        <!-- 仪表板 -->
        <div id="dashboard" class="tab-content active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="serviceStatus">未知</div>
                    <div class="stat-label">服务状态</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="uploadTraffic">0 B</div>
                    <div class="stat-label">上传流量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="downloadTraffic">0 B</div>
                    <div class="stat-label">下载流量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="activeConnections">0</div>
                    <div class="stat-label">活跃连接</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">快速操作</div>
                <div class="card-body">
                    <button class="btn" onclick="refreshStatus()">刷新状态</button>
                    <button class="btn btn-success" onclick="getEndpoints()">获取端点</button>
                    <button class="btn btn-warning" onclick="getTraffic()">获取流量</button>
                    <button class="btn" onclick="getConnections()">获取连接</button>
                    <button class="btn btn-danger" onclick="clearLogs()">清空日志</button>
                </div>
            </div>
        </div>

        <!-- 端点管理 -->
        <div id="endpoints" class="tab-content">
            <div class="card">
                <div class="card-header">网络端点列表</div>
                <div class="card-body">
                    <button class="btn" onclick="loadEndpoints()">
                        <span class="loading hidden" id="endpointsLoading"></span>
                        刷新端点列表
                    </button>
                    <div id="endpointsList" style="margin-top: 15px;">
                        <p>点击"刷新端点列表"加载网络端点</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置管理 -->
        <div id="config" class="tab-content">
            <div class="card">
                <div class="card-header">配置文件管理</div>
                <div class="card-body">
                    <div class="form-group">
                        <button class="btn" onclick="loadConfig()">加载配置</button>
                        <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                        <button class="btn btn-warning" onclick="backupConfig()">备份配置</button>
                        <button class="btn" onclick="listBackups()">查看备份</button>
                    </div>
                    <div class="form-group">
                        <label for="configEditor">配置内容 (YAML格式)</label>
                        <textarea id="configEditor" placeholder="配置文件内容将在这里显示..."></textarea>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">配置备份</div>
                <div class="card-body">
                    <div id="backupsList">
                        <p>点击"查看备份"加载备份列表</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 流量统计 -->
        <div id="traffic" class="tab-content">
            <div class="card">
                <div class="card-header">流量统计</div>
                <div class="card-body">
                    <button class="btn" onclick="refreshTraffic()">刷新流量</button>
                    <div class="stats-grid" style="margin-top: 20px;">
                        <div class="stat-card">
                            <div class="stat-value" id="totalUpload">0 B</div>
                            <div class="stat-label">总上传</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="totalDownload">0 B</div>
                            <div class="stat-label">总下载</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="uploadSpeed">0 B/s</div>
                            <div class="stat-label">上传速度</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="downloadSpeed">0 B/s</div>
                            <div class="stat-label">下载速度</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 连接管理 -->
        <div id="connections" class="tab-content">
            <div class="card">
                <div class="card-header">活跃连接</div>
                <div class="card-body">
                    <button class="btn" onclick="loadConnections()">刷新连接</button>
                    <div id="connectionsList" style="margin-top: 15px;">
                        <p>点击"刷新连接"加载连接列表</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志查看 -->
        <div id="logs" class="tab-content">
            <div class="card">
                <div class="card-header">操作日志</div>
                <div class="card-body">
                    <button class="btn" onclick="clearLogs()">清空日志</button>
                    <button class="btn" onclick="exportLogs()">导出日志</button>
                    <div class="log-container" id="logContainer" style="margin-top: 15px;">
                        欢迎使用网络管理控制台！
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统设置 -->
        <div id="settings" class="tab-content">
            <div class="card">
                <div class="card-header">🔗 连接设置</div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="protocol">协议类型</label>
                        <select id="protocol" onchange="updateApiUrl()">
                            <option value="http">HTTP (应用层加密)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="apiHost">服务器地址</label>
                        <input type="text" id="apiHost" value="*************" placeholder="IP地址或域名">
                    </div>
                    <div class="form-group">
                        <label for="apiPort">端口</label>
                        <input type="number" id="apiPort" value="11214" placeholder="端口号" min="1" max="65535">
                    </div>
                    <div class="form-group">
                        <label for="apiUrl">完整API地址 <small>(自动生成)</small></label>
                        <input type="text" id="apiUrl" value="" readonly style="background-color: #f5f5f5;">
                        <small class="form-text">根据上述设置自动生成的完整API地址</small>
                    </div>
                    <div class="form-group">
                        <label for="apiToken">API 令牌</label>
                        <input type="password" id="apiToken" value="jhxnb666" placeholder="输入API令牌">
                    </div>

                    <div class="form-group">
                        <button class="btn btn-success" onclick="saveSettings()">💾 保存设置</button>
                        <button class="btn" onclick="loadSettings()">🔄 重置设置</button>
                        <button class="btn btn-info" onclick="testConnection()">🔍 测试连接</button>
                    </div>
                    <div id="connectionStatus" style="margin-top: 10px;"></div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">自动刷新设置</div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="autoRefresh">自动刷新间隔 (秒)</label>
                        <select id="autoRefresh" onchange="setAutoRefresh()">
                            <option value="0">关闭</option>
                            <option value="5">5秒</option>
                            <option value="10">10秒</option>
                            <option value="30" selected>30秒</option>
                            <option value="60">60秒</option>
                        </select>
                    </div>
                    <p style="color: #666; font-size: 14px; margin-top: 10px;">
                        自动刷新将定期更新仪表板数据
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let isConnected = false;
        let autoRefreshInterval = null;
        let currentTab = 'dashboard';

        // 加密工具类（使用 Fernet 兼容 Python 后端）
        class CryptoManager {
            constructor(secretKey = 'zfmi_crypto_secret_key') {
                this.secretKey = secretKey;
                this.salt = 'stable-salt-for-key-derivation';
                this.iterations = 100000;

                // 派生 Fernet 密钥
                this.fernetKey = this.deriveFernetKey(secretKey);
                this.secret = new fernet.Secret(this.fernetKey);
            }

            // 从密钥派生 Fernet 密钥
            deriveFernetKey(secretKey) {
                // 使用 PBKDF2 派生 32 字节密钥，与 Python 后端保持一致
                const key = CryptoJS.PBKDF2(secretKey, this.salt, {
                    keySize: 32 / 4, // 32 bytes = 8 words (4 bytes each)
                    iterations: this.iterations,
                    hasher: CryptoJS.algo.SHA256
                });

                // 转换为 base64 格式（Fernet 要求）
                return CryptoJS.enc.Base64.stringify(key);
            }

            // 加密数据（兼容 Python Fernet 格式）
            encryptData(data) {
                try {
                    // 将数据转换为 JSON 字符串
                    const jsonStr = JSON.stringify(data);

                    // 使用 Fernet 加密
                    const token = new fernet.Token({
                        secret: this.secret
                    });

                    return token.encode(jsonStr);
                } catch (error) {
                    console.error('数据加密失败:', error);
                    throw error;
                }
            }

            // 解密数据
            decryptData(encryptedData) {
                try {
                    // 使用 Fernet 解密
                    const token = new fernet.Token({
                        secret: this.secret,
                        token: encryptedData,
                        ttl: 0 // 不检查过期时间
                    });

                    const jsonStr = token.decode();
                    return JSON.parse(jsonStr);
                } catch (error) {
                    console.error('数据解密失败:', error);
                    throw error;
                }
            }
        }

        // 安全请求包装器
        class SecureRequest {
            constructor(cryptoManager) {
                this.crypto = cryptoManager;
            }

            // 加密请求数据
            encryptRequest(data) {
                const encryptedData = this.crypto.encryptData(data);
                return {
                    encrypted: true,
                    data: encryptedData,
                    timestamp: new Date().toISOString()
                };
            }

            // 解密请求数据
            decryptRequest(requestData) {
                if (!requestData.encrypted) {
                    return requestData;
                }
                return this.crypto.decryptData(requestData.data);
            }

            // 加密响应数据
            encryptResponse(data) {
                return this.encryptRequest(data);
            }

            // 解密响应数据
            decryptResponse(responseData) {
                return this.decryptRequest(responseData);
            }
        }

        // 全局加密管理器实例
        const cryptoManager = new CryptoManager();
        const secureRequest = new SecureRequest(cryptoManager);

        // 工具函数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatTime() {
            return new Date().toLocaleTimeString();
        }

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = formatTime();
            const logEntry = `[${timestamp}] ${message}\n`;

            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showAlert(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            const activeTab = document.querySelector('.tab-content.active .card-body');
            if (activeTab) {
                activeTab.insertBefore(alert, activeTab.firstChild);
                setTimeout(() => alert.remove(), 3000);
            }
        }

        function updateConnectionStatus(connected) {
            isConnected = connected;
            const dot = document.getElementById('connectionDot');
            const text = document.getElementById('connectionText');

            if (connected) {
                dot.classList.add('connected');
                text.textContent = '已连接';
            } else {
                dot.classList.remove('connected');
                text.textContent = '未连接';
            }
        }

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有导航项的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');

            // 添加active类到对应的导航项
            event.target.classList.add('active');

            // 更新页面标题
            const titles = {
                'dashboard': '仪表板',
                'proxies': '代理管理',
                'config': '配置管理',
                'traffic': '流量统计',
                'connections': '连接管理',
                'logs': '日志查看',
                'settings': '系统设置'
            };

            document.getElementById('pageTitle').textContent = titles[tabName] || '未知页面';
            currentTab = tabName;
        }

        // API调用函数（支持加密）
        async function makeApiCall(endpoint, options = {}) {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiToken = document.getElementById('apiToken').value.trim();

            if (!apiUrl || !apiToken) {
                throw new Error('请先配置API地址和令牌');
            }

            const url = `${apiUrl}${endpoint}`;

            // 准备请求选项
            const requestOptions = {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json',
                    // 'X-Encrypted': 'true', // 暂时禁用加密
                },
                mode: 'cors', // 处理跨域请求
                ...options
            };

            // 暂时禁用请求体加密
            // if (requestOptions.body && requestOptions.method &&
            //     ['POST', 'PUT', 'PATCH'].includes(requestOptions.method.toUpperCase())) {
            //     try {
            //         const requestData = JSON.parse(requestOptions.body);
            //         const encryptedRequest = secureRequest.encryptRequest(requestData);
            //         requestOptions.body = JSON.stringify(encryptedRequest);
            //     } catch (error) {
            //         console.error('请求数据加密失败:', error);
            //         throw new Error('请求数据加密失败');
            //     }
            // }

            try {
                const response = await fetch(url, requestOptions);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseData = await response.json();

                // 如果响应是加密的，进行解密
                if (responseData.encrypted) {
                    try {
                        return secureRequest.decryptResponse(responseData);
                    } catch (error) {
                        console.error('响应数据解密失败:', error);
                        throw new Error('响应数据解密失败');
                    }
                }

                return responseData;
            } catch (error) {
                // 处理网络错误
                if (error.message.includes('CORS')) {
                    throw new Error('跨域请求被阻止。请确保服务器配置了正确的CORS策略。');
                } else if (error.message.includes('加密') || error.message.includes('解密')) {
                    throw error; // 保持加密相关的错误信息
                }

                // 重新抛出其他错误
                throw error;
            }
        }



        // 刷新状态
        async function refreshStatus() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('📊 正在获取服务状态...');

            try {
                const result = await makeApiCall('/api/service/status');

                if (result.success) {
                    const data = result.data;

                    // 更新状态显示
                    document.getElementById('serviceStatus').textContent =
                        data.service_status === 'running' ? '🟢 运行中' : '🔴 已停止';

                    if (data.traffic) {
                        document.getElementById('uploadTraffic').textContent =
                            formatBytes(data.traffic.up || 0);
                        document.getElementById('downloadTraffic').textContent =
                            formatBytes(data.traffic.down || 0);
                    }

                    log(`✅ 状态获取成功 - 版本: ${data.version?.version || 'Unknown'}`);
                    showAlert('状态更新成功', 'success');
                } else {
                    throw new Error(result.message || '获取状态失败');
                }
            } catch (error) {
                log(`❌ 获取状态失败: ${error.message}`);
                showAlert(`获取状态失败: ${error.message}`, 'error');
            }
        }

        // 端点管理函数
        async function getEndpoints() {
            return loadEndpoints();
        }

        async function loadEndpoints() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            const loading = document.getElementById('endpointsLoading');
            loading.classList.remove('hidden');

            log('🔗 正在获取端点信息...');

            try {
                const result = await makeApiCall('/api/service/endpoints');

                if (result.success) {
                    const endpoints = result.data.endpoints || {};
                    const endpointCount = Object.keys(endpoints).length;

                    log(`✅ 端点信息获取成功，共 ${endpointCount} 个网络端点`);

                    // 渲染端点列表
                    renderEndpointsList(endpoints);

                    showAlert(`获取到 ${endpointCount} 个网络端点`, 'success');
                } else {
                    throw new Error(result.message || '获取端点信息失败');
                }
            } catch (error) {
                log(`❌ 获取端点信息失败: ${error.message}`);
                showAlert(`获取端点信息失败: ${error.message}`, 'error');
                document.getElementById('endpointsList').innerHTML = '<p>获取端点信息失败</p>';
            } finally {
                loading.classList.add('hidden');
            }
        }

        function renderEndpointsList(endpoints) {
            const container = document.getElementById('endpointsList');
            let html = '';

            for (const [name, endpoint] of Object.entries(endpoints)) {
                const delay = endpoint.history && endpoint.history.length > 0 ?
                    endpoint.history[endpoint.history.length - 1].delay : 'N/A';

                html += `
                    <div class="endpoint-item">
                        <div class="endpoint-info">
                            <div class="endpoint-name">${name}</div>
                            <div class="endpoint-details">
                                类型: ${endpoint.type || 'Unknown'} |
                                延迟: ${delay}ms |
                                状态: ${endpoint.now || 'N/A'}
                            </div>
                        </div>
                        <div class="endpoint-actions">
                            <button class="btn" onclick="testEndpointDelay('${name}')">测试延迟</button>
                        </div>
                    </div>
                `;
            }

            container.innerHTML = html || '<p>没有找到网络端点</p>';
        }

        async function testEndpointDelay(endpointName) {
            log(`🔍 正在测试端点 ${endpointName} 的延迟...`);
            showAlert(`正在测试 ${endpointName} 延迟...`, 'info');
        }

        // 流量统计函数
        async function getTraffic() {
            return refreshTraffic();
        }

        async function refreshTraffic() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('📈 正在获取流量统计...');

            try {
                const result = await makeApiCall('/api/service/traffic');

                if (result.success) {
                    const traffic = result.data;

                    // 更新流量显示
                    document.getElementById('totalUpload').textContent =
                        formatBytes(traffic.up || 0);
                    document.getElementById('totalDownload').textContent =
                        formatBytes(traffic.down || 0);

                    // 同时更新仪表板的流量显示
                    document.getElementById('uploadTraffic').textContent =
                        formatBytes(traffic.up || 0);
                    document.getElementById('downloadTraffic').textContent =
                        formatBytes(traffic.down || 0);

                    log(`✅ 流量统计: 上传 ${formatBytes(traffic.up || 0)}, 下载 ${formatBytes(traffic.down || 0)}`);
                    showAlert('流量统计更新成功', 'success');
                } else {
                    throw new Error(result.message || '获取流量统计失败');
                }
            } catch (error) {
                log(`❌ 获取流量统计失败: ${error.message}`);
                showAlert(`获取流量统计失败: ${error.message}`, 'error');
            }
        }

        // 连接管理函数
        async function getConnections() {
            return loadConnections();
        }

        async function loadConnections() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('🌐 正在获取连接信息...');

            try {
                const result = await makeApiCall('/api/service/connections');

                if (result.success) {
                    const connections = result.data.connections || [];

                    log(`✅ 连接信息获取成功，共 ${connections.length} 个活跃连接`);

                    // 渲染连接列表
                    renderConnectionsList(connections);

                    // 更新仪表板的连接数
                    document.getElementById('activeConnections').textContent = connections.length;

                    showAlert(`获取到 ${connections.length} 个活跃连接`, 'success');
                } else {
                    throw new Error(result.message || '获取连接信息失败');
                }
            } catch (error) {
                log(`❌ 获取连接信息失败: ${error.message}`);
                showAlert(`获取连接信息失败: ${error.message}`, 'error');
                document.getElementById('connectionsList').innerHTML = '<p>获取连接信息失败</p>';
            }
        }

        function renderConnectionsList(connections) {
            const container = document.getElementById('connectionsList');

            if (connections.length === 0) {
                container.innerHTML = '<p>当前没有活跃连接</p>';
                return;
            }

            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>目标地址</th>
                            <th>协议</th>
                            <th>代理</th>
                            <th>上传</th>
                            <th>下载</th>
                            <th>开始时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            connections.forEach(conn => {
                html += `
                    <tr>
                        <td>${conn.metadata?.host || conn.metadata?.destinationIP || 'N/A'}:${conn.metadata?.destinationPort || ''}</td>
                        <td>${conn.metadata?.network || 'N/A'}</td>
                        <td>${conn.chains?.[0] || 'DIRECT'}</td>
                        <td>${formatBytes(conn.upload || 0)}</td>
                        <td>${formatBytes(conn.download || 0)}</td>
                        <td>${new Date(conn.start).toLocaleTimeString()}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // 配置管理函数
        async function loadConfig() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('⚙️ 正在加载配置文件...');

            try {
                const result = await makeApiCall('/api/config/config.yaml');

                if (result.success) {
                    const configData = result.data;

                    // 将配置转换为YAML字符串显示
                    const yamlString = JSON.stringify(configData, null, 2);
                    document.getElementById('configEditor').value = yamlString;

                    log('✅ 配置文件加载成功');
                    showAlert('配置文件加载成功', 'success');
                } else {
                    throw new Error(result.message || '加载配置文件失败');
                }
            } catch (error) {
                log(`❌ 加载配置文件失败: ${error.message}`);
                showAlert(`加载配置文件失败: ${error.message}`, 'error');
            }
        }

        async function saveConfig() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            const configText = document.getElementById('configEditor').value.trim();
            if (!configText) {
                showAlert('配置内容不能为空', 'error');
                return;
            }

            log('💾 正在保存配置文件...');

            try {
                // 尝试解析JSON
                const configData = JSON.parse(configText);

                const result = await makeApiCall('/api/config/config.yaml', {
                    method: 'PUT',
                    body: JSON.stringify(configData)
                });

                if (result.success) {
                    log('✅ 配置文件保存成功');
                    showAlert('配置文件保存成功', 'success');
                } else {
                    throw new Error(result.message || '保存配置文件失败');
                }
            } catch (error) {
                if (error instanceof SyntaxError) {
                    log(`❌ 配置格式错误: ${error.message}`);
                    showAlert('配置格式错误，请检查JSON格式', 'error');
                } else {
                    log(`❌ 保存配置文件失败: ${error.message}`);
                    showAlert(`保存配置文件失败: ${error.message}`, 'error');
                }
            }
        }

        async function backupConfig() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('📦 正在备份配置文件...');

            try {
                const result = await makeApiCall('/api/config/backup', {
                    method: 'POST'
                });

                if (result.success) {
                    log(`✅ 配置文件备份成功: ${result.data.backup_path}`);
                    showAlert('配置文件备份成功', 'success');
                } else {
                    throw new Error(result.message || '备份配置文件失败');
                }
            } catch (error) {
                log(`❌ 备份配置文件失败: ${error.message}`);
                showAlert(`备份配置文件失败: ${error.message}`, 'error');
            }
        }

        async function listBackups() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('📋 正在获取备份列表...');

            try {
                const result = await makeApiCall('/api/config/backups');

                if (result.success) {
                    const backups = result.data;

                    log(`✅ 备份列表获取成功，共 ${backups.length} 个备份`);

                    // 渲染备份列表
                    renderBackupsList(backups);

                    showAlert(`找到 ${backups.length} 个备份文件`, 'success');
                } else {
                    throw new Error(result.message || '获取备份列表失败');
                }
            } catch (error) {
                log(`❌ 获取备份列表失败: ${error.message}`);
                showAlert(`获取备份列表失败: ${error.message}`, 'error');
            }
        }

        function renderBackupsList(backups) {
            const container = document.getElementById('backupsList');

            if (backups.length === 0) {
                container.innerHTML = '<p>没有找到备份文件</p>';
                return;
            }

            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>大小</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            backups.forEach(backup => {
                html += `
                    <tr>
                        <td>${backup.filename}</td>
                        <td>${formatBytes(backup.size)}</td>
                        <td>${new Date(backup.created).toLocaleString()}</td>
                        <td>
                            <button class="btn" onclick="downloadBackup('${backup.filename}')">下载</button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function downloadBackup(filename) {
            log(`📥 准备下载备份文件: ${filename}`);
            showAlert(`备份下载功能需要服务器支持`, 'info');
        }

        // 日志管理函数
        function clearLogs() {
            document.getElementById('logContainer').textContent = '';
            log('📝 日志已清空');
        }

        function exportLogs() {
            const logs = document.getElementById('logContainer').textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `network-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            log('📤 日志已导出');
            showAlert('日志导出成功', 'success');
        }

        // 连接管理函数
        function updateApiUrl() {
            const protocol = document.getElementById('protocol').value;
            const host = document.getElementById('apiHost').value.trim();
            const port = document.getElementById('apiPort').value.trim();

            if (host && port) {
                const apiUrl = `${protocol}://${host}:${port}`;
                document.getElementById('apiUrl').value = apiUrl;
            }
        }

        async function testConnection() {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiToken = document.getElementById('apiToken').value.trim();
            const statusDiv = document.getElementById('connectionStatus');

            if (!apiUrl || !apiToken) {
                statusDiv.innerHTML = '<div style="color: #dc3545;">❌ 请先填写API地址和令牌</div>';
                return;
            }

            statusDiv.innerHTML = '<div style="color: #007bff;">🔄 正在测试连接...</div>';

            try {
                const response = await fetch(`${apiUrl}/api/service/status`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiToken}`,
                        'Content-Type': 'application/json',
                    },
                    // 处理CORS问题
                    mode: 'cors',
                });

                if (response.ok) {
                    const data = await response.json();
                    statusDiv.innerHTML = '<div style="color: #28a745;">✅ 连接成功！服务器响应正常</div>';
                    log('🔗 API连接测试成功');
                    // 更新连接状态
                    updateConnectionStatus(true);
                } else {
                    statusDiv.innerHTML = `<div style="color: #dc3545;">❌ 连接失败: HTTP ${response.status}</div>`;
                    // 更新连接状态为失败
                    updateConnectionStatus(false);
                }
            } catch (error) {
                let errorMsg = '连接失败';
                if (error.message.includes('CORS')) {
                    errorMsg = '跨域请求被阻止，请检查服务器CORS配置';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMsg = '网络连接失败，请检查地址和端口';
                }

                statusDiv.innerHTML = `<div style="color: #dc3545;">❌ ${errorMsg}</div>`;
                log(`🔗 API连接测试失败: ${error.message}`);
                // 更新连接状态为失败
                updateConnectionStatus(false);
            }
        }

        // 设置管理函数
        function saveSettings() {
            const protocol = document.getElementById('protocol').value;
            const apiHost = document.getElementById('apiHost').value.trim();
            const apiPort = document.getElementById('apiPort').value.trim();
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiToken = document.getElementById('apiToken').value.trim();

            if (!apiHost || !apiPort || !apiToken) {
                showAlert('服务器地址、端口和令牌不能为空', 'error');
                return;
            }

            // 保存到localStorage
            localStorage.setItem('network_protocol', protocol);
            localStorage.setItem('network_api_host', apiHost);
            localStorage.setItem('network_api_port', apiPort);
            localStorage.setItem('network_api_url', apiUrl);
            localStorage.setItem('network_api_token', apiToken);

            log('💾 设置已保存');
            showAlert('设置保存成功', 'success');
        }

        function loadSettings() {
            // 重置为默认值
            document.getElementById('protocol').value = 'http';
            document.getElementById('apiHost').value = '*************';
            document.getElementById('apiPort').value = '11214';
            document.getElementById('apiToken').value = 'jhxnb666';
            updateApiUrl();

            document.getElementById('protocol').value = savedProtocol;
            document.getElementById('apiHost').value = savedHost;
            document.getElementById('apiPort').value = savedPort;
            document.getElementById('apiToken').value = savedToken;

            if (savedUrl) {
                document.getElementById('apiUrl').value = savedUrl;
            } else {
                updateApiUrl(); // 自动生成URL
            }

            log('📂 设置已加载');
        }

        // 自动刷新管理
        function setAutoRefresh() {
            const interval = parseInt(document.getElementById('autoRefresh').value);

            // 清除现有的定时器
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }

            if (interval > 0) {
                autoRefreshInterval = setInterval(() => {
                    if (isConnected && currentTab === 'dashboard') {
                        refreshStatus();
                    }
                }, interval * 1000);

                log(`⏰ 自动刷新已设置为 ${interval} 秒`);
                showAlert(`自动刷新已设置为 ${interval} 秒`, 'info');
            } else {
                log('⏰ 自动刷新已关闭');
                showAlert('自动刷新已关闭', 'info');
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎉 网络管理控制台已加载完成');
            log('💡 提示: 请确保网络服务正在运行，并且API端口(8888)可访问');

            // 加载保存的设置
            loadSettings();

            // 设置自动刷新
            setAutoRefresh();

            // 自动测试连接
            setTimeout(testConnection, 1000);
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'Enter':
                        e.preventDefault();
                        testConnection();
                        break;
                    case 'r':
                        e.preventDefault();
                        refreshStatus();
                        break;
                    case 's':
                        e.preventDefault();
                        if (currentTab === 'config') {
                            saveConfig();
                        } else if (currentTab === 'settings') {
                            saveSettings();
                        }
                        break;
                }
            }
        });

        // 窗口关闭前清理
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>

        // 工具函数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatTime() {
            return new Date().toLocaleTimeString();
        }

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = formatTime();
            const logEntry = `[${timestamp}] ${message}\n`;

            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showAlert(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            const activeTab = document.querySelector('.tab-content.active .card-body');
            if (activeTab) {
                activeTab.insertBefore(alert, activeTab.firstChild);
                setTimeout(() => alert.remove(), 3000);
            }
        }

        function updateConnectionStatus(connected) {
            isConnected = connected;
            const dot = document.getElementById('connectionDot');
            const text = document.getElementById('connectionText');

            if (connected) {
                dot.classList.add('connected');
                text.textContent = '已连接';
            } else {
                dot.classList.remove('connected');
                text.textContent = '未连接';
            }
        }

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有导航项的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');

            // 添加active类到对应的导航项
            event.target.classList.add('active');

            // 更新页面标题
            const titles = {
                'dashboard': '仪表板',
                'proxies': '代理管理',
                'config': '配置管理',
                'traffic': '流量统计',
                'connections': '连接管理',
                'logs': '日志查看',
                'settings': '系统设置'
            };

            document.getElementById('pageTitle').textContent = titles[tabName] || '未知页面';
            currentTab = tabName;
        }



        // 刷新状态
        async function refreshStatus() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            log('📊 正在获取服务状态...');

            try {
                const result = await makeApiCall('/api/service/status');

                if (result.success) {
                    const data = result.data;

                    // 更新状态显示
                    document.getElementById('serviceStatus').textContent =
                        data.service_status === 'running' ? '🟢 运行中' : '🔴 已停止';

                    if (data.traffic) {
                        document.getElementById('uploadTraffic').textContent =
                            formatBytes(data.traffic.up || 0);
                        document.getElementById('downloadTraffic').textContent =
                            formatBytes(data.traffic.down || 0);
                    }

                    log(`✅ 状态获取成功 - 版本: ${data.version?.version || 'Unknown'}`);
                    showAlert('状态更新成功', 'success');
                } else {
                    throw new Error(result.message || '获取状态失败');
                }
            } catch (error) {
                log(`❌ 获取状态失败: ${error.message}`);
                showAlert(`获取状态失败: ${error.message}`, 'error');
            }
        }

        // 获取端点信息
        async function getEndpoints() {
            return loadEndpoints();
        }

        async function loadEndpoints() {
            if (!isConnected) {
                showAlert('请先测试连接', 'error');
                return;
            }

            const loading = document.getElementById('endpointsLoading');
            loading.classList.remove('hidden');

            log('🔗 正在获取端点信息...');

            try {
                const result = await makeApiCall('/api/service/endpoints');

                if (result.success) {
                    const proxies = result.data.proxies || {};
                    const proxyCount = Object.keys(proxies).length;

                    log(`✅ 代理信息获取成功，共 ${proxyCount} 个代理节点`);

                    // 渲染代理列表
                    renderProxiesList(proxies);

                    showAlert(`获取到 ${proxyCount} 个代理节点`, 'success');
                } else {
                    throw new Error(result.message || '获取代理信息失败');
                }
            } catch (error) {
                log(`❌ 获取代理信息失败: ${error.message}`);
                showAlert(`获取代理信息失败: ${error.message}`, 'error');
                document.getElementById('proxiesList').innerHTML = '<p>获取代理信息失败</p>';
            } finally {
                loading.classList.add('hidden');
            }
        }

    <div class="main-content">
        <div class="header">
            <h1 id="pageTitle">仪表板</h1>
            <div class="connection-info">
                <div class="status-dot" id="connectionDot"></div>
                <span id="connectionText">未连接</span>
                <button class="btn btn-success" onclick="testConnection()">测试连接</button>
            </div>
        </div>

        <!-- 仪表板 -->
        <div id="dashboard" class="tab-content active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="serviceStatus">未知</div>
                    <div class="stat-label">服务状态</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="uploadTraffic">0 B</div>
                    <div class="stat-label">上传流量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="downloadTraffic">0 B</div>
                    <div class="stat-label">下载流量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="activeConnections">0</div>
                    <div class="stat-label">活跃连接</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">快速操作</div>
                <div class="card-body">
                    <button class="btn" onclick="refreshStatus()">刷新状态</button>
                    <button class="btn btn-success" onclick="getEndpoints()">获取代理</button>
                    <button class="btn btn-warning" onclick="getTraffic()">获取流量</button>
                    <button class="btn" onclick="getConnections()">获取连接</button>
                    <button class="btn btn-danger" onclick="clearLogs()">清空日志</button>
                </div>
            </div>
        </div>

        <!-- 代理管理 -->
        <div id="proxies" class="tab-content">
            <div class="card">
                <div class="card-header">代理节点列表</div>
                <div class="card-body">
                    <button class="btn" onclick="loadEndpoints()">
                        <span class="loading hidden" id="endpointsLoading"></span>
                        刷新端点列表
                    </button>
                    <div id="endpointsList" style="margin-top: 15px;">
                        <p>点击"刷新端点列表"加载网络端点</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置管理 -->
        <div id="config" class="tab-content">
            <div class="card">
                <div class="card-header">配置文件管理</div>
                <div class="card-body">
                    <div class="form-group">
                        <button class="btn" onclick="loadConfig()">加载配置</button>
                        <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                        <button class="btn btn-warning" onclick="backupConfig()">备份配置</button>
                        <button class="btn" onclick="listBackups()">查看备份</button>
                    </div>
                    <div class="form-group">
                        <label for="configEditor">配置内容 (YAML格式)</label>
                        <textarea id="configEditor" placeholder="配置文件内容将在这里显示..."></textarea>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">配置备份</div>
                <div class="card-body">
                    <div id="backupsList">
                        <p>点击"查看备份"加载备份列表</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 流量统计 -->
        <div id="traffic" class="tab-content">
            <div class="card">
                <div class="card-header">流量统计</div>
                <div class="card-body">
                    <button class="btn" onclick="refreshTraffic()">刷新流量</button>
                    <div class="stats-grid" style="margin-top: 20px;">
                        <div class="stat-card">
                            <div class="stat-value" id="totalUpload">0 B</div>
                            <div class="stat-label">总上传</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="totalDownload">0 B</div>
                            <div class="stat-label">总下载</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="uploadSpeed">0 B/s</div>
                            <div class="stat-label">上传速度</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="downloadSpeed">0 B/s</div>
                            <div class="stat-label">下载速度</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 连接管理 -->
        <div id="connections" class="tab-content">
            <div class="card">
                <div class="card-header">活跃连接</div>
                <div class="card-body">
                    <button class="btn" onclick="loadConnections()">刷新连接</button>
                    <div id="connectionsList" style="margin-top: 15px;">
                        <p>点击"刷新连接"加载连接列表</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志查看 -->
        <div id="logs" class="tab-content">
            <div class="card">
                <div class="card-header">操作日志</div>
                <div class="card-body">
                    <button class="btn" onclick="clearLogs()">清空日志</button>
                    <button class="btn" onclick="exportLogs()">导出日志</button>
                    <div class="log-container" id="logContainer" style="margin-top: 15px;">
                        欢迎使用网络管理控制台！
                    </div>
                </div>
            </div>
        </div>


    </div>