# mihomo 前端控制面板

这是一个简单易用的 mihomo 代理服务前端管理界面，提供了两个不同的页面来管理你的 mihomo 服务。

## 页面说明

### 1. index.html - 简单控制面板
- **功能**: 基础的状态查看和操作
- **特点**: 界面简洁，操作简单
- **适用**: 日常快速查看和基本操作

**主要功能**:
- ✅ 连接测试
- ✅ 服务状态查看
- ✅ 流量统计显示
- ✅ 代理信息获取
- ✅ 操作日志记录

### 2. admin.html - 高级管理面板
- **功能**: 完整的管理功能
- **特点**: 功能丰富，界面专业
- **适用**: 高级用户和详细管理

**主要功能**:
- 📊 **仪表板**: 实时状态监控
- 🔗 **代理管理**: 查看和管理代理节点
- ⚙️ **配置管理**: 编辑和备份配置文件
- 📈 **流量统计**: 详细的流量分析
- 🌐 **连接管理**: 查看活跃连接
- 📝 **日志查看**: 操作日志管理
- 🔧 **系统设置**: 连接配置和自动刷新

## 使用方法

### 1. 启动 mihomo API 服务
确保你的 mihomo API 服务正在运行：

```bash
# 使用 Docker 运行
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  jhxxr/mihomo:latest

# 或者直接运行 Python API
cd api
python main.py
```

### 2. 访问前端页面

#### 方法一：直接打开HTML文件
```bash
# 打开简单面板
open web/index.html

# 打开高级面板
open web/admin.html
```

#### 方法二：使用HTTP服务器
```bash
# 进入web目录
cd web

# 使用Python启动HTTP服务器
python3 -m http.server 8080

# 或使用Node.js
npx http-server -p 8080

# 然后访问
# http://localhost:8080/index.html
# http://localhost:8080/admin.html
```

### 3. 配置连接信息

在页面中配置以下信息：
- **API 地址**: `http://localhost:8888` (默认)
- **API 令牌**: `jhxnb666` (默认)

### 4. 测试连接

点击"测试连接"按钮，确保前端可以正常连接到 mihomo API 服务。

## API 接口说明

前端页面使用以下 API 接口：

| 接口 | 方法 | 说明 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/api/mihomo/status` | GET | 获取服务状态 |
| `/api/mihomo/proxies` | GET | 获取代理信息 |
| `/api/mihomo/traffic` | GET | 获取流量统计 |
| `/api/mihomo/connections` | GET | 获取连接信息 |
| `/api/config/config.yaml` | GET/PUT | 获取/更新配置 |
| `/api/config/backup` | POST | 备份配置 |
| `/api/config/backups` | GET | 获取备份列表 |

## 功能特性

### 🔒 安全认证
- 使用 Bearer Token 认证
- 支持自定义 API 地址和令牌

### 📱 响应式设计
- 支持桌面和移动设备
- 自适应布局

### ⚡ 实时更新
- 自动刷新功能
- 可配置刷新间隔

### 💾 本地存储
- 自动保存连接设置
- 支持配置导入导出

### 🎨 美观界面
- 现代化设计风格
- 直观的操作体验

## 故障排除

### 1. 连接失败
- 检查 mihomo API 服务是否运行
- 确认 API 地址和端口正确
- 检查防火墙设置

### 2. CORS 跨域问题
如果遇到跨域问题，可以：
- 使用 HTTP 服务器访问页面
- 配置浏览器允许跨域
- 修改 API 服务的 CORS 设置

### 3. 功能异常
- 查看浏览器控制台错误信息
- 检查 API 令牌是否正确
- 确认 mihomo 服务版本兼容性

## 开发说明

### 技术栈
- **前端**: 纯 HTML + CSS + JavaScript
- **后端**: Python FastAPI
- **认证**: Bearer Token
- **通信**: RESTful API

### 自定义开发
你可以根据需要修改页面：
1. 编辑 HTML 文件添加新功能
2. 修改 CSS 样式自定义外观
3. 扩展 JavaScript 函数增加交互

### API 扩展
如需添加新功能，可以：
1. 在 `api/main.py` 中添加新的路由
2. 在前端页面中调用新的 API
3. 更新界面显示新的数据

## 许可证

本项目基于 MIT 许可证开源。

## 贡献

欢迎提交 Issue 和 Pull Request！

---

**提示**: 这些前端页面是为了方便管理 mihomo 服务而创建的，请确保在安全的网络环境中使用。
