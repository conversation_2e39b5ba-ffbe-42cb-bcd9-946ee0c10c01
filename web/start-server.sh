#!/bin/bash

# mihomo 前端服务器启动脚本

PORT=${1:-8080}
HOST=${2:-localhost}

echo "🚀 启动 mihomo 前端服务器..."
echo "📍 地址: http://$HOST:$PORT"
echo "📄 简单面板: http://$HOST:$PORT/index.html"
echo "🔧 高级面板: http://$HOST:$PORT/admin.html"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================="

# 检查Python是否可用
if command -v python3 &> /dev/null; then
    echo "使用 Python3 启动服务器..."
    python3 -m http.server $PORT --bind $HOST
elif command -v python &> /dev/null; then
    echo "使用 Python 启动服务器..."
    python -m http.server $PORT --bind $HOST
elif command -v npx &> /dev/null; then
    echo "使用 Node.js 启动服务器..."
    npx http-server -p $PORT -a $HOST
else
    echo "❌ 错误: 未找到 Python 或 Node.js"
    echo "请安装 Python 3 或 Node.js 来启动HTTP服务器"
    exit 1
fi
