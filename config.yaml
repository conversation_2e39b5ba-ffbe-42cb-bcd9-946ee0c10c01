# ========== 基础设置 ==========
allow-lan: true
bind-address: "*"
mode: rule
log-level: debug

external-controller: 0.0.0.0:11024 # RESTful API 监听地址
secret: "jhxnb666" # RESTful API 密钥

# ========== 出站代理节点列表 ==========
proxies:
  - name: "🇭🇰 [直连]香港anytls[xxc][新协议]"
    server: ************** 
    port: 43233
    type: anytls
    password: dc797c09-9520-4ce8-9254-bb8ea3363eb1
    sni: steam.wapis.7878007.xyz
    skip-cert-verify: true
    udp: true
    tfo: false

  - name: "🇭🇰 [直连]香港[new协议][xxc]"
    server: music.hyh.7878007.xyz
    port: 4344
    type: tuic
    uuid: dc797c09-9520-4ce8-9254-bb8ea3363eb1
    password: dc797c09-9520-4ce8-9254-bb8ea3363eb1
    alpn: [h3]
    udp-relay-mode: quic
    congestion-controller: bbr
    sni: steam.wapis.7878007.xyz
    skip-cert-verify: true
    udp: true
    tfo: false

# ========== 代理组 ==========
proxy-groups:
  # 自动测速组（URLTest/health-check）
  - name: auto-test
    type: url-test
    proxies:
      - "🇭🇰 [直连]香港anytls[xxc][新协议]"
      - "🇭🇰 [直连]香港[new协议][xxc]"
    url: https://www.gstatic.com/generate_204
    interval: 600  # 增加检查间隔以减少频繁检查
    timeout: 5000  # 设置超时时间为5秒
    tolerance: 50  # 设置延迟容忍度

  # 手动选择组（Select）
  - name: manual-select
    type: select
    proxies:
      - "🇭🇰 [直连]香港anytls[xxc][新协议]"
      - "🇭🇰 [直连]香港[new协议][xxc]"

  # 负载均衡组（轮询/Load-Balance）
  - name: load-balance
    type: load-balance
    strategy: round-robin
    proxies:
      - "🇭🇰 [直连]香港anytls[xxc][新协议]"
      - "🇭🇰 [直连]香港[new协议][xxc]"

  # 汇总组：将上面三个组作为“出站标签”
  - name: ALL-PROXY
    type: select
    proxies:
      - auto-test
      - manual-select
      - load-balance

# ========== 隧道转发 ==========
tunnels:
  - network: [tcp, udp]
    address: 0.0.0.0:12790
    target: **************:4333
    proxy: ALL-PROXY
  - tcp/udp,0.0.0.0:65000,**************:65000,load-balance
  - tcp/udp,0.0.0.0:65001,**************:65001,load-balance
  - tcp/udp,0.0.0.0:65002,**************:65002,load-balance
  - tcp/udp,0.0.0.0:65003,**************:65003,load-balance
  - tcp/udp,0.0.0.0:65004,**************:65004,load-balance
  - tcp/udp,0.0.0.0:65005,**************:65005,load-balance

# ========== 其他部分保持官方模板 ==========
# mixed-port、dns、routing、external-controller 等按需填充 :contentReference[oaicite:0]{index=0}
