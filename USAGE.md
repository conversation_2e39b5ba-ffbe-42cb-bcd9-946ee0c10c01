# mihomo Docker 使用指南

## 快速部署

### 1. 使用 Docker 命令

```bash
# 拉取镜像
docker pull your-username/mihomo:latest

# 运行容器（主机网络模式）
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  --cap-add NET_RAW \
  your-username/mihomo:latest

# 查看运行状态
docker ps | grep mihomo

# 查看日志
docker logs -f mihomo
```

### 2. 使用 Docker Compose

```bash
# 下载 docker-compose.yml
wget https://raw.githubusercontent.com/your-username/mihomo/main/docker-compose.yml

# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 配置说明

### 默认端口

- **10801**: HTTP/SOCKS5 混合代理端口
- **11024**: Web 管理界面和 API 端口
- **12790**: 隧道转发端口

### 访问管理界面

1. 打开浏览器访问: `http://localhost:11024`
2. 输入密钥: `jhxnb666`

### 代理设置

#### 系统代理设置

**Windows:**
1. 设置 → 网络和 Internet → 代理
2. 手动设置代理
3. HTTP 代理: `127.0.0.1:10801`
4. SOCKS 代理: `127.0.0.1:10801`

**macOS:**
1. 系统偏好设置 → 网络
2. 选择网络接口 → 高级 → 代理
3. HTTP 代理: `127.0.0.1:10801`
4. SOCKS 代理: `127.0.0.1:10801`

**Linux:**
```bash
export http_proxy=http://127.0.0.1:10801
export https_proxy=http://127.0.0.1:10801
export ALL_PROXY=socks5://127.0.0.1:10801
```

#### 浏览器代理设置

**Chrome/Edge:**
使用 SwitchyOmega 插件，配置代理服务器为 `127.0.0.1:10801`

**Firefox:**
设置 → 网络设置 → 手动代理配置
- HTTP 代理: `127.0.0.1:10801`
- SOCKS 代理: `127.0.0.1:10801`

## 自定义配置

### 1. 挂载自定义配置文件

```bash
# 创建配置目录
mkdir -p ./config

# 复制默认配置
docker run --rm your-username/mihomo:latest cat /etc/mihomo/config.yaml > ./config/config.yaml

# 编辑配置文件
nano ./config/config.yaml

# 使用自定义配置运行
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  -v $(pwd)/config/config.yaml:/etc/mihomo/config.yaml:ro \
  your-username/mihomo:latest
```

### 2. 环境变量配置

```bash
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  -e MIHOMO_LOG_LEVEL=debug \
  -e TZ=Asia/Shanghai \
  your-username/mihomo:latest
```

## 管理命令

### 容器管理

```bash
# 启动容器
docker start mihomo

# 停止容器
docker stop mihomo

# 重启容器
docker restart mihomo

# 删除容器
docker rm -f mihomo

# 查看容器信息
docker inspect mihomo
```

### 日志管理

```bash
# 查看实时日志
docker logs -f mihomo

# 查看最近100行日志
docker logs --tail 100 mihomo

# 查看指定时间的日志
docker logs --since "2024-01-01T00:00:00" mihomo
```

### 进入容器

```bash
# 进入容器 shell
docker exec -it mihomo sh

# 查看配置文件
docker exec mihomo cat /etc/mihomo/config.yaml

# 查看进程状态
docker exec mihomo ps aux
```

## API 使用

### 基本 API 调用

```bash
# 获取版本信息
curl -H "Authorization: Bearer jhxnb666" \
  http://localhost:11024/version

# 获取配置信息
curl -H "Authorization: Bearer jhxnb666" \
  http://localhost:11024/configs

# 获取代理列表
curl -H "Authorization: Bearer jhxnb666" \
  http://localhost:11024/proxies
```

### 代理切换

```bash
# 切换到指定代理
curl -X PUT \
  -H "Authorization: Bearer jhxnb666" \
  -H "Content-Type: application/json" \
  -d '{"name":"🇭🇰 [直连]香港anytls[xxc][新协议]"}' \
  http://localhost:11024/proxies/ALL-PROXY

# 获取当前代理状态
curl -H "Authorization: Bearer jhxnb666" \
  http://localhost:11024/proxies/ALL-PROXY
```

## 故障排除

### 1. 容器无法启动

```bash
# 检查容器状态
docker ps -a | grep mihomo

# 查看启动日志
docker logs mihomo

# 检查配置文件语法
docker run --rm -v $(pwd)/config.yaml:/tmp/config.yaml your-username/mihomo:latest \
  mihomo -t -f /tmp/config.yaml
```

### 2. 网络连接问题

```bash
# 检查端口监听
netstat -tlnp | grep -E "(10801|11024|12790)"

# 测试代理连接
curl --proxy http://127.0.0.1:10801 https://www.google.com

# 测试 API 连接
curl http://localhost:11024/version
```

### 3. 权限问题

```bash
# 检查容器权限
docker inspect mihomo | grep -A 10 "CapAdd"

# 重新运行容器并添加权限
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  --cap-add NET_RAW \
  --privileged \
  your-username/mihomo:latest
```

## 性能优化

### 1. 资源限制

```bash
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  --memory 512m \
  --cpus 1.0 \
  your-username/mihomo:latest
```

### 2. 日志轮转

```bash
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  --log-driver json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  your-username/mihomo:latest
```

## 更新升级

### 1. 更新镜像

```bash
# 拉取最新镜像
docker pull your-username/mihomo:latest

# 停止并删除旧容器
docker stop mihomo && docker rm mihomo

# 启动新容器
docker run -d \
  --name mihomo \
  --network host \
  --restart unless-stopped \
  --cap-add NET_ADMIN \
  your-username/mihomo:latest
```

### 2. 使用 Docker Compose 更新

```bash
# 拉取最新镜像
docker-compose pull

# 重新创建容器
docker-compose up -d --force-recreate
```
