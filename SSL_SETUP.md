# SSL/HTTPS 配置指南

本指南将帮助您为 ZFMI API 服务配置 SSL/HTTPS 加密通信，即使在使用 IP 地址访问的情况下也能确保数据传输安全。

## 🔒 为什么需要 SSL 加密？

- **数据安全**: 防止敏感信息（如 API 密钥、配置数据）在传输过程中被窃取
- **身份验证**: 确保连接到正确的服务器
- **数据完整性**: 防止数据在传输过程中被篡改
- **合规要求**: 满足安全标准和最佳实践

## 📋 前置要求

- 已安装 OpenSSL（大多数 Linux 发行版默认包含）
- 具有执行脚本的权限
- 了解您的服务器 IP 地址

## 🚀 快速开始

### 1. 生成 SSL 证书

使用提供的脚本自动生成自签名 SSL 证书：

```bash
# 使用自动检测的 IP 地址
bash scripts/generate_ssl_cert.sh

# 或指定特定的 IP 地址
bash scripts/generate_ssl_cert.sh *************
```

脚本将会：
- 自动检测您的本机 IP 地址
- 生成支持多个域名和 IP 的证书
- 创建 `ssl/` 目录并保存证书文件

### 2. 启用 SSL 配置

#### 方法 A: 环境变量配置（推荐）

创建 `.env` 文件或设置环境变量：

```bash
# 启用 SSL
export ENABLE_SSL=true

# SSL 证书路径（默认值，通常不需要修改）
export SSL_CERT_FILE=./ssl/api-server.crt
export SSL_KEY_FILE=./ssl/api-server.key

# HTTPS 端口（默认 8443）
export SSL_PORT=8443

# 是否同时启用 HTTP（默认 true）
export ENABLE_HTTP=true
```

#### 方法 B: Docker Compose 配置

修改 `docker-compose.yml` 中的环境变量：

```yaml
environment:
  - ENABLE_SSL=true
  - SSL_PORT=8443
  - ENABLE_HTTP=true
```

### 3. 启动服务

```bash
# 直接运行
python api/main.py

# 或使用 Docker
docker-compose up -d
```

## 🌐 访问方式

启用 SSL 后，您可以通过以下方式访问 API：

- **HTTPS**: `https://your-ip:8443/api/`
- **HTTP**: `http://your-ip:8888/api/` （如果启用了 HTTP）

## 🔧 配置选项详解

### SSL 相关环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `ENABLE_SSL` | `false` | 是否启用 SSL/HTTPS |
| `SSL_CERT_FILE` | `./ssl/api-server.crt` | SSL 证书文件路径 |
| `SSL_KEY_FILE` | `./ssl/api-server.key` | SSL 私钥文件路径 |
| `SSL_PORT` | `8443` | HTTPS 监听端口 |
| `ENABLE_HTTP` | `true` | 是否同时启用 HTTP |

### 运行模式

1. **仅 HTTP** (默认):
   ```bash
   ENABLE_SSL=false
   ENABLE_HTTP=true
   ```

2. **仅 HTTPS**:
   ```bash
   ENABLE_SSL=true
   ENABLE_HTTP=false
   ```

3. **HTTP + HTTPS** (推荐):
   ```bash
   ENABLE_SSL=true
   ENABLE_HTTP=true
   ```

## 🔍 证书详细信息

生成的证书支持以下访问方式：

- `localhost`
- `127.0.0.1`
- `::1` (IPv6 localhost)
- 自动检测的本机 IP 地址
- 用户指定的额外 IP 地址

证书有效期：**10 年**

## 🌍 浏览器配置

由于使用自签名证书，浏览器会显示安全警告。以下是处理方法：

### Chrome/Edge
1. 访问 HTTPS 地址时，点击"高级"
2. 点击"继续前往 [IP地址]（不安全）"
3. 或者将证书添加到受信任的根证书颁发机构

### Firefox
1. 访问 HTTPS 地址时，点击"高级"
2. 点击"接受风险并继续"

### 添加证书到系统信任列表

#### Windows
```cmd
# 以管理员身份运行
certlm.msc
# 导入证书到"受信任的根证书颁发机构"
```

#### macOS
```bash
# 添加到钥匙串
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain ssl/api-server.crt
```

#### Linux (Ubuntu/Debian)
```bash
# 复制证书到系统目录
sudo cp ssl/api-server.crt /usr/local/share/ca-certificates/zfmi-api.crt
sudo update-ca-certificates
```

## 🔧 故障排除

### 证书文件不存在
```
SSL证书文件不存在，请先生成SSL证书
运行以下命令生成证书:
  bash scripts/generate_ssl_cert.sh
```

**解决方案**: 运行证书生成脚本

### 端口被占用
```
Error: [Errno 98] Address already in use
```

**解决方案**: 
1. 检查端口占用：`netstat -tlnp | grep 8443`
2. 修改 SSL_PORT 环境变量
3. 停止占用端口的进程

### 权限问题
```
Permission denied: '/app/ssl/api-server.key'
```

**解决方案**:
```bash
# 设置正确的文件权限
chmod 600 ssl/api-server.key
chmod 644 ssl/api-server.crt
```

## 📱 Web 端配置

更新 Web 面板中的 API 地址：

```javascript
// 将 API 地址改为 HTTPS
const apiUrl = 'https://your-ip:8443';
```

## 🔄 证书更新

证书有效期为 10 年，如需更新：

```bash
# 删除旧证书
rm -rf ssl/

# 重新生成
bash scripts/generate_ssl_cert.sh
```

## 📊 性能影响

启用 SSL 会有轻微的性能开销：
- CPU 使用率增加约 1-3%
- 延迟增加约 1-5ms
- 内存使用增加约 10-20MB

对于大多数使用场景，这些开销是可以接受的。

## 🛡️ 安全建议

1. **定期更新证书**: 虽然有效期很长，建议每年更新一次
2. **限制访问**: 使用防火墙限制 HTTPS 端口的访问
3. **强密码**: 确保 API_SECRET 足够复杂
4. **监控日志**: 定期检查访问日志，发现异常访问
5. **备份证书**: 将证书文件备份到安全位置

## 📞 技术支持

如果遇到问题，请检查：
1. 证书文件是否存在且权限正确
2. 端口是否被占用
3. 防火墙是否允许相应端口
4. 环境变量是否正确设置

更多信息请参考项目文档或提交 Issue。
