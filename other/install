#!/bin/bash

# 设置版本号
VERSION="v1.19.11"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检测系统架构
detect_arch() {
    local arch=$(uname -m)
    case $arch in
        x86_64|amd64)
            echo "linux-amd64-compatible-go123"
            ;;
        aarch64|arm64)
            echo "linux-arm64"
            ;;
        armv7l|armhf)
            echo "linux-armv7"
            ;;
        i386|i686)
            echo "linux-386"
            ;;
        *)
            log_error "不支持的系统架构: $arch"
            exit 1
            ;;
    esac
}

# 检查是否以root权限运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 root 权限运行此脚本"
        exit 1
    fi
}

# 检查并删除现有安装
check_existing_installation() {
    log_info "检查现有 mihomo 安装..."
    
    # 停止服务
    if systemctl is-active --quiet mihomo; then
        log_info "停止 mihomo 服务..."
        systemctl stop mihomo
    fi
    
    # 禁用服务
    if systemctl is-enabled --quiet mihomo; then
        log_info "禁用 mihomo 服务..."
        systemctl disable mihomo
    fi
    
    # 删除二进制文件
    if [ -f "/usr/local/bin/mihomo" ]; then
        log_info "删除现有 mihomo 二进制文件..."
        rm -f /usr/local/bin/mihomo
    fi
    
    # 删除服务文件
    if [ -f "/etc/systemd/system/mihomo.service" ]; then
        log_info "删除现有 systemd 服务文件..."
        rm -f /etc/systemd/system/mihomo.service
        systemctl daemon-reload
    fi
    
    # 备份现有配置
    if [ -d "/etc/mihomo" ]; then
        log_warning "发现现有配置目录 /etc/mihomo"
        read -p "是否备份现有配置？[Y/n]: " backup_choice
        if [[ $backup_choice =~ ^[Nn]$ ]]; then
            log_info "跳过配置备份"
        else
            backup_dir="/etc/mihomo.backup.$(date +%Y%m%d_%H%M%S)"
            cp -r /etc/mihomo "$backup_dir"
            log_success "配置已备份到: $backup_dir"
        fi
    fi
    
    log_success "清理完成"
}

# 选择下载源
select_download_source() {
    echo
    log_info "请选择下载源："
    echo "1) GitHub 官方源 (默认)"
    echo "2) 中国加速源 (ghproxy.com)"
    echo "3) 中国加速源 (mirror.ghproxy.com)"
    
    while true; do
        read -p "请选择 [1-3, 默认为1]: " choice
        case $choice in
            1|"")
                BASE_URL="https://github.com/MetaCubeX/mihomo/releases/download"
                log_info "使用 GitHub 官方源"
                break
                ;;
            2)
                BASE_URL="https://hub.gitmirror.com/https://github.com/MetaCubeX/mihomo/releases/download"
                log_info "使用中国加速源 (hub.gitmirror)"
                break
                ;;
            3)
                BASE_URL="https://github.moeyy.xyz/https://github.com/MetaCubeX/mihomo/releases/download"
                log_info "使用中国加速源 (mirror.ghproxy.com)"
                break
                ;;
            *)
                log_warning "无效选择，请重新输入"
                ;;
        esac
    done
}

# 下载并安装 mihomo
download_and_install() {
    local arch=$(detect_arch)
    local filename="mihomo-${arch}-${VERSION}.gz"
    local download_url="${BASE_URL}/${VERSION}/${filename}"
    
    log_info "检测到系统架构: $arch"
    log_info "下载 mihomo $VERSION ..."
    
    # 创建目录
    mkdir -p /etc/mihomo
    
    # 下载文件
    log_info "下载地址: $download_url"
    if curl -L --progress-bar "$download_url" | gunzip -c > /usr/local/bin/mihomo; then
        chmod 755 /usr/local/bin/mihomo
        log_success "mihomo 下载并安装成功"
    else
        log_error "下载失败，请检查网络连接或尝试其他下载源"
        exit 1
    fi
    
}

# 处理配置文件
handle_config() {
    log_info "处理配置文件..."
    
    if [ -f "./config.yaml" ]; then
        cp ./config.yaml /etc/mihomo/
        log_success "配置文件已复制到 /etc/mihomo/"
    else
        log_warning "当前目录下未找到 config.yaml"
        echo "请手动将配置文件复制到 /etc/mihomo/config.yaml"
        echo "或者从以下地址下载示例配置："
        echo "https://github.com/MetaCubeX/mihomo/blob/Alpha/docs/config.yaml"
    fi
}

# 创建 systemd 服务
create_systemd_service() {
    log_info "创建 systemd 服务文件..."
    
    cat << 'EOF' > /etc/systemd/system/mihomo.service
[Unit]
Description=mihomo Daemon, Another Clash Kernel.
After=network.target NetworkManager.service systemd-networkd.service iwd.service

[Service]
Type=simple
LimitNPROC=500
LimitNOFILE=1000000
CapabilityBoundingSet=CAP_NET_ADMIN CAP_NET_RAW CAP_NET_BIND_SERVICE CAP_SYS_TIME CAP_SYS_PTRACE CAP_DAC_READ_SEARCH CAP_DAC_OVERRIDE
AmbientCapabilities=CAP_NET_ADMIN CAP_NET_RAW CAP_NET_BIND_SERVICE CAP_SYS_TIME CAP_SYS_PTRACE CAP_DAC_READ_SEARCH CAP_DAC_OVERRIDE
Restart=always
ExecStartPre=/usr/bin/sleep 1s
ExecStart=/usr/local/bin/mihomo -d /etc/mihomo
ExecReload=/bin/kill -HUP $MAINPID

[Install]
WantedBy=multi-user.target
EOF
    
    log_success "systemd 服务文件创建完成"
}

# 启用并启动服务
enable_and_start_service() {
    log_info "重新加载 systemd..."
    systemctl daemon-reload
    
    log_info "启用 mihomo 服务..."
    systemctl enable mihomo
    
    # 检查配置文件是否存在
    if [ ! -f "/etc/mihomo/config.yaml" ]; then
        log_warning "未找到配置文件，跳过服务启动"
        log_info "请配置好 /etc/mihomo/config.yaml 后运行: systemctl start mihomo"
        return
    fi
    
    log_info "启动 mihomo 服务..."
    if systemctl start mihomo; then
        log_success "mihomo 服务启动成功"
    else
        log_error "mihomo 服务启动失败"
        log_info "请检查配置文件并查看日志: journalctl -u mihomo -f"
        return
    fi
}

# 显示状态和使用说明
show_status_and_usage() {
    echo
    log_info "检查 mihomo 状态："
    systemctl status mihomo --no-pager -l
    
    echo
    log_success "安装完成！"
    echo
    echo "📋 常用命令："
    echo "  启动服务: systemctl start mihomo"
    echo "  停止服务: systemctl stop mihomo"
    echo "  重启服务: systemctl restart mihomo"
    echo "  查看状态: systemctl status mihomo"
    echo "  查看日志: journalctl -u mihomo -f"
    echo "  编辑配置: nano /etc/mihomo/config.yaml"
    echo
    echo "📁 重要路径："
    echo "  二进制文件: /usr/local/bin/mihomo"
    echo "  配置目录: /etc/mihomo/"
    echo "  服务文件: /etc/systemd/system/mihomo.service"
}

# 主函数
main() {
    echo "🚀 mihomo 自动安装脚本 $VERSION"
    echo "======================================"
    
    # 检查权限
    check_root
    
    # 检查现有安装
    check_existing_installation
    
    # 选择下载源
    select_download_source
    
    # 下载并安装
    download_and_install
    
    # 处理配置文件
    handle_config
    
    # 创建服务
    create_systemd_service
    
    # 启用并启动服务
    enable_and_start_service
    
    # 显示状态和使用说明
    show_status_and_usage
}

# 运行主函数
main "$@"