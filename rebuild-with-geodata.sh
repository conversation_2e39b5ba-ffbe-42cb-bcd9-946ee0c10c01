#!/bin/bash

# 重新构建包含地理数据的Docker镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=== 重新构建 ZFMI 镜像（包含地理数据） ==="

# 1. 停止现有容器
log_info "停止现有容器..."
docker stop zfmi 2>/dev/null || true
docker rm zfmi 2>/dev/null || true

# 2. 删除旧镜像（可选）
read -p "是否删除旧镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "删除旧镜像..."
    docker rmi jhxxr/zfmi:latest 2>/dev/null || true
fi

# 3. 构建新镜像
log_info "构建新镜像（包含地理数据文件）..."
docker build -t jhxxr/zfmi:latest . || {
    log_error "镜像构建失败"
    exit 1
}

log_success "镜像构建完成"

# 4. 验证镜像内容
log_info "验证镜像内容..."
docker run --rm jhxxr/zfmi:latest /bin/sh -c "ls -la /etc/mihomo/*.mmdb /etc/mihomo/*.dat 2>/dev/null || echo 'No geodata files found'"

# 5. 启动新容器
log_info "启动新容器..."
docker run -d \
  --name zfmi \
  --network host \
  --restart unless-stopped \
  --privileged \
  --cap-add NET_ADMIN \
  --cap-add NET_RAW \
  --cap-add NET_BIND_SERVICE \
  --device /dev/net/tun:/dev/net/tun \
  --env TZ=Asia/Shanghai \
  --env API_SECRET=jhxnb666 \
  --env MIHOMO_SECRET=jhxnb666 \
  --env SUPERVISOR_ENABLED=true \
  --env MIHOMO_LOG_LEVEL=info \
  --env LOG_LEVEL=info \
  --volume /lib/modules:/lib/modules:ro \
  --volume /etc/resolv.conf:/etc/resolv.conf:ro \
  --volume "$(pwd)/config.yaml:/etc/mihomo/config.yaml:ro" \
  jhxxr/zfmi:latest

log_success "容器启动完成"

# 6. 等待服务启动
log_info "等待服务启动..."
sleep 10

# 7. 检查服务状态
log_info "检查服务状态..."

# 检查容器状态
if docker ps -f name=zfmi --format "{{.Status}}" | grep -q "Up"; then
    log_success "容器运行正常"
else
    log_error "容器启动失败"
    docker logs --tail 20 zfmi
    exit 1
fi

# 检查API服务
if curl -s --connect-timeout 5 http://localhost:8888/health | grep -q "ok"; then
    log_success "Python API 服务正常"
else
    log_warning "Python API 服务异常"
fi

# 检查mihomo API
sleep 5
if curl -s --connect-timeout 5 -H "Authorization: Bearer jhxnb666" http://localhost:11024/version >/dev/null 2>&1; then
    log_success "mihomo API 服务正常"
    echo "mihomo版本信息:"
    curl -s -H "Authorization: Bearer jhxnb666" http://localhost:11024/version | head -3
else
    log_warning "mihomo API 服务异常，查看日志..."
    docker logs --tail 10 zfmi
fi

# 8. 显示端口监听状态
log_info "端口监听状态:"
netstat -tlnp 2>/dev/null | grep -E "(10801|11024|8888)" || echo "未发现相关端口监听"

# 9. 测试代理功能
log_info "测试代理功能..."
if curl -s --connect-timeout 5 --proxy http://localhost:10801 http://www.google.com >/dev/null 2>&1; then
    log_success "代理功能正常"
else
    log_warning "代理功能测试失败"
fi

echo ""
log_success "=== 重新构建完成 ==="
echo ""
echo "服务信息:"
echo "  HTTP/SOCKS5代理: localhost:10801"
echo "  管理API: http://localhost:11024"
echo "  配置API: http://localhost:8888"
echo ""
echo "查看日志: docker logs -f zfmi"
echo "进入容器: docker exec -it zfmi /bin/sh"
