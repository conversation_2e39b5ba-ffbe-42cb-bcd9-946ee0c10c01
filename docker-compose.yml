version: '3.8'

services:
  zfmi:
    build: .
    container_name: zfmi-server
    restart: unless-stopped
    network_mode: host
    privileged: false
    cap_add:
      - NET_ADMIN
      - NET_RAW
    volumes:
      # 配置文件挂载
      - ./config.yaml:/etc/mihomo/config.yaml:ro
      - ./data/backups:/etc/mihomo/backups
      - ./data/logs:/var/log/mihomo
      # 移除 SSL 证书挂载，使用应用层加密
    environment:
      # 基础配置
      - MIHOMO_CONFIG_FILE=/etc/mihomo/config.yaml
      - MIHOMO_BACKUP_DIR=/etc/mihomo/backups
      - LOG_DIR=/var/log/mihomo
      - LOG_LEVEL=info
      - DEBUG=false
      
      # API配置
      - API_HOST=0.0.0.0
      - API_PORT=8888
      - API_SECRET=${API_SECRET:-jhxnb666}
      
      # mihomo配置
      - MIHOMO_API_BASE=http://localhost:11024
      - MIHOMO_SECRET=${MIHOMO_SECRET:-jhxnb666}
      - MIHOMO_API_TIMEOUT=10
      
      # 移除 SSL 配置，使用应用层加密
      
      # CORS配置
      - CORS_ORIGINS=*
      - CORS_ALLOW_CREDENTIALS=true
      
      # 监控配置
      - SUPERVISOR_ENABLED=true
      - HEALTH_CHECK_INTERVAL=30
      - MAX_BACKUP_FILES=10
    healthcheck:
      test: ["CMD", "/entrypoint.sh", "healthcheck"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 30s
    labels:
      - "com.docker.compose.project=zfmi"
      - "com.docker.compose.service=zfmi-server"

  # Web面板服务（可选，如果需要独立部署）
  zfmi-web:
    image: nginx:alpine
    container_name: zfmi-web
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./web:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # SSL证书挂载（如果需要web也支持HTTPS）
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - zfmi
    profiles:
      - web
    labels:
      - "com.docker.compose.project=zfmi"
      - "com.docker.compose.service=zfmi-web"

# 数据卷定义
volumes:
  zfmi-data:
    driver: local
  zfmi-logs:
    driver: local

# 网络定义（如果不使用host网络）
networks:
  zfmi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
