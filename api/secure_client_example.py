#!/usr/bin/env python3
"""
安全客户端示例
演示如何使用加密通信与 API 服务器交互
"""

import json
import requests
from crypto_utils import CryptoManager, SecureRequest

class SecureAPIClient:
    """安全 API 客户端"""
    
    def __init__(self, base_url: str, secret_key: str = None):
        """初始化客户端
        
        Args:
            base_url: API 服务器地址
            secret_key: 加密密钥（需要与服务器一致）
        """
        self.base_url = base_url.rstrip('/')
        self.crypto_manager = CryptoManager(secret_key)
        self.secure_request = SecureRequest(self.crypto_manager)
        self.session = requests.Session()
        self.token = None
        
        # 设置默认头部
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-Encrypted': 'true'  # 启用加密
        })
    
    def login(self, username: str, password: str) -> bool:
        """登录获取令牌
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            登录是否成功
        """
        try:
            # 准备登录数据
            login_data = {
                'username': username,
                'password': password
            }
            
            # 加密请求数据
            encrypted_data = self.secure_request.encrypt_request(login_data)
            
            # 发送登录请求
            response = self.session.post(
                f"{self.base_url}/api/auth/token",
                json=encrypted_data
            )
            
            if response.status_code == 200:
                # 解密响应
                response_data = response.json()
                if response_data.get('encrypted'):
                    decrypted_data = self.secure_request.decrypt_response(response_data)
                else:
                    decrypted_data = response_data
                
                # 保存令牌
                self.token = decrypted_data.get('access_token')
                if self.token:
                    self.session.headers['Authorization'] = f'Bearer {self.token}'
                    return True
            
            return False
        except Exception as e:
            print(f"登录失败: {e}")
            return False
    
    def _make_request(self, method: str, endpoint: str, data: dict = None) -> dict:
        """发送加密请求
        
        Args:
            method: HTTP 方法
            endpoint: API 端点
            data: 请求数据
            
        Returns:
            响应数据
        """
        url = f"{self.base_url}{endpoint}"
        
        # 准备请求数据
        request_data = None
        if data:
            request_data = self.secure_request.encrypt_request(data)
        
        # 发送请求
        response = self.session.request(
            method=method,
            url=url,
            json=request_data
        )
        
        if response.status_code == 200:
            response_data = response.json()
            # 解密响应
            if response_data.get('encrypted'):
                return self.secure_request.decrypt_response(response_data)
            else:
                return response_data
        else:
            raise Exception(f"请求失败: {response.status_code} - {response.text}")
    
    def get_service_status(self) -> dict:
        """获取服务状态"""
        return self._make_request('GET', '/api/service/status')
    
    def get_service_version(self) -> dict:
        """获取服务版本"""
        return self._make_request('GET', '/api/service/version')
    
    def get_service_endpoints(self) -> dict:
        """获取服务端点"""
        return self._make_request('GET', '/api/service/endpoints')
    
    def get_config(self) -> dict:
        """获取配置"""
        return self._make_request('GET', '/api/config/config.yaml')
    
    def update_config(self, config_data: dict) -> dict:
        """更新配置"""
        return self._make_request('PUT', '/api/config/config.yaml', config_data)

def main():
    """示例用法"""
    # 创建客户端（使用与服务器相同的密钥）
    client = SecureAPIClient(
        base_url="http://localhost:8888",
        secret_key="your-secret-key-here"  # 与服务器配置一致
    )
    
    try:
        # 登录
        print("正在登录...")
        if client.login("admin", "your-api-secret"):
            print("登录成功！")
            
            # 获取服务状态
            print("\n获取服务状态...")
            status = client.get_service_status()
            print(f"服务状态: {status}")
            
            # 获取版本信息
            print("\n获取版本信息...")
            version = client.get_service_version()
            print(f"版本信息: {version}")
            
            # 获取配置
            print("\n获取配置...")
            config = client.get_config()
            print(f"配置获取成功，包含 {len(config.get('data', {}))} 个配置项")
            
        else:
            print("登录失败！")
    
    except Exception as e:
        print(f"操作失败: {e}")

if __name__ == "__main__":
    main()
