#!/usr/bin/env python3
"""
加密工具模块
提供应用层数据加密/解密功能
"""

import os
import json
import base64
import hashlib
import secrets
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)

class CryptoManager:
    """加密管理器"""
    
    def __init__(self, secret_key: str = None):
        """初始化加密管理器
        
        Args:
            secret_key: 主密钥，如果为空则从环境变量获取
        """
        self.secret_key = secret_key or os.getenv('CRYPTO_SECRET_KEY', 'zfmi_crypto_secret_key')
        self.salt = b'stable-salt-for-key-derivation'  # 生产环境应该使用随机盐
        self._fernet = None
        self._init_fernet()
    
    def _init_fernet(self):
        """初始化 Fernet 加密器"""
        # 使用 PBKDF2 从密钥派生加密密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
            backend=default_backend()
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.secret_key.encode()))
        self._fernet = Fernet(key)
    
    def encrypt_data(self, data: Any) -> str:
        """加密数据
        
        Args:
            data: 要加密的数据
            
        Returns:
            加密后的 base64 字符串
        """
        try:
            # 将数据转换为 JSON 字符串
            json_str = json.dumps(data, ensure_ascii=False)
            
            # 加密数据
            encrypted_data = self._fernet.encrypt(json_str.encode('utf-8'))
            
            # 返回 base64 编码的字符串
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            logger.error(f"数据加密失败: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> Any:
        """解密数据
        
        Args:
            encrypted_data: 加密的 base64 字符串
            
        Returns:
            解密后的原始数据
        """
        try:
            # 解码 base64
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            
            # 解密数据
            decrypted_bytes = self._fernet.decrypt(encrypted_bytes)
            
            # 转换回原始数据
            json_str = decrypted_bytes.decode('utf-8')
            return json.loads(json_str)
        except Exception as e:
            logger.error(f"数据解密失败: {e}")
            raise
    
    def create_secure_token(self, payload: Dict[str, Any], expires_minutes: int = 60) -> str:
        """创建安全令牌
        
        Args:
            payload: 令牌载荷
            expires_minutes: 过期时间（分钟）
            
        Returns:
            加密的令牌字符串
        """
        # 添加时间戳和过期时间
        now = datetime.utcnow()
        token_data = {
            'payload': payload,
            'issued_at': now.isoformat(),
            'expires_at': (now + timedelta(minutes=expires_minutes)).isoformat(),
            'nonce': secrets.token_hex(16)  # 防重放攻击
        }
        
        return self.encrypt_data(token_data)
    
    def verify_secure_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证安全令牌
        
        Args:
            token: 加密的令牌字符串
            
        Returns:
            如果有效返回载荷，否则返回 None
        """
        try:
            token_data = self.decrypt_data(token)
            
            # 检查过期时间
            expires_at = datetime.fromisoformat(token_data['expires_at'])
            if datetime.utcnow() > expires_at:
                logger.warning("令牌已过期")
                return None
            
            return token_data['payload']
        except Exception as e:
            logger.error(f"令牌验证失败: {e}")
            return None
    
    def hash_password(self, password: str) -> str:
        """哈希密码
        
        Args:
            password: 明文密码
            
        Returns:
            哈希后的密码
        """
        return hashlib.pbkdf2_hmac('sha256', password.encode(), self.salt, 100000).hex()
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码
        
        Args:
            password: 明文密码
            hashed: 哈希密码
            
        Returns:
            密码是否匹配
        """
        return self.hash_password(password) == hashed

class SecureRequest:
    """安全请求包装器"""
    
    def __init__(self, crypto_manager: CryptoManager):
        self.crypto = crypto_manager
    
    def encrypt_request(self, data: Dict[str, Any]) -> Dict[str, str]:
        """加密请求数据
        
        Args:
            data: 请求数据
            
        Returns:
            包含加密数据的字典
        """
        encrypted_data = self.crypto.encrypt_data(data)
        return {
            'encrypted': True,
            'data': encrypted_data,
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def decrypt_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """解密请求数据
        
        Args:
            request_data: 加密的请求数据
            
        Returns:
            解密后的原始数据
        """
        if not request_data.get('encrypted'):
            return request_data
        
        return self.crypto.decrypt_data(request_data['data'])
    
    def encrypt_response(self, data: Dict[str, Any]) -> Dict[str, str]:
        """加密响应数据
        
        Args:
            data: 响应数据
            
        Returns:
            包含加密数据的字典
        """
        return self.encrypt_request(data)
    
    def decrypt_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """解密响应数据
        
        Args:
            response_data: 加密的响应数据
            
        Returns:
            解密后的原始数据
        """
        return self.decrypt_request(response_data)

# 全局加密管理器实例
crypto_manager = CryptoManager()
secure_request = SecureRequest(crypto_manager)
