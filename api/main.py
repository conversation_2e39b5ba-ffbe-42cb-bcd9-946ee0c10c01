#!/usr/bin/env python3
"""
网络管理 API 服务器
提供配置管理和数据传输功能
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import yaml
import aiohttp
import asyncio
from datetime import datetime, timedelta
import hashlib
import secrets

# 导入配置
from config import config
from crypto_utils import crypto_manager, secure_request

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL.upper()),
    format='[API] %(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout,
    force=True
)
logger = logging.getLogger(__name__)

# 打印配置信息
if config.DEBUG:
    config.print_config()

# 创建 FastAPI 应用
app = FastAPI(
    title="Network Management API",
    description="Secure API for network configuration and monitoring services",
    version="1.0.0",
    docs_url=None,  # 禁用文档页面
    redoc_url=None  # 禁用文档页面
)

# 配置 CORS - 简化并修复跨域支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=["*"],  # 允许所有头部
    expose_headers=["*"],
    max_age=3600,
)

# 添加加密中间件
@app.middleware("http")
async def encryption_middleware(request: Request, call_next):
    """加密中间件 - 处理请求和响应的加密"""
    # 检查是否需要加密
    use_encryption = request.headers.get("X-Encrypted") == "true"

    if use_encryption and request.method in ["POST", "PUT", "PATCH"]:
        # 解密请求体
        try:
            body = await request.body()
            if body:
                encrypted_data = json.loads(body.decode())
                if encrypted_data.get("encrypted"):
                    decrypted_data = secure_request.decrypt_request(encrypted_data)
                    # 重新构造请求
                    request._body = json.dumps(decrypted_data).encode()
        except Exception as e:
            logger.error(f"请求解密失败: {e}")
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid encrypted request"}
            )

    # 处理请求
    response = await call_next(request)

    # 如果需要加密响应
    if use_encryption and response.status_code == 200:
        try:
            # 获取响应内容
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk

            if response_body:
                response_data = json.loads(response_body.decode())
                encrypted_response = secure_request.encrypt_response(response_data)

                return JSONResponse(
                    content=encrypted_response,
                    status_code=response.status_code,
                    headers=dict(response.headers)
                )
        except Exception as e:
            logger.error(f"响应加密失败: {e}")

    return response

# 添加OPTIONS处理器来处理预检请求
@app.options("/{path:path}")
async def options_handler():
    """处理所有OPTIONS预检请求"""
    return JSONResponse(
        content={"message": "OK"},
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "3600",
        }
    )

# 安全认证
security = HTTPBearer()

class AuthManager:
    """认证管理器"""

    def __init__(self):
        self.api_key = config.API_SECRET
        self.tokens = {}  # 简单的内存存储，生产环境应使用数据库

    def verify_token(self, token: str) -> bool:
        """验证访问令牌"""
        # 尝试验证加密令牌
        payload = crypto_manager.verify_secure_token(token)
        if payload:
            return payload.get('username') == 'admin'

        # 兼容旧的简单令牌
        return token == self.api_key

    def generate_token(self, username: str, password: str) -> Optional[str]:
        """生成访问令牌"""
        # 验证用户凭据
        if username == "admin" and password == self.api_key:
            # 生成加密令牌
            payload = {
                'username': username,
                'role': 'admin',
                'permissions': ['read', 'write', 'admin']
            }
            return crypto_manager.create_secure_token(payload, expires_minutes=480)  # 8小时有效期
        return None

auth_manager = AuthManager()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户（验证令牌）"""
    if not auth_manager.verify_token(credentials.credentials):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return {"username": "admin"}

class ConfigManager:
    """配置文件管理器"""

    def __init__(self, config_file: str = None):
        self.config_file = Path(config_file or config.CONFIG_FILE)
        self.backup_dir = Path(config.BACKUP_DIR)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    async def read_config(self) -> Dict[str, Any]:
        """读取配置文件"""
        try:
            if not self.config_file.exists():
                raise HTTPException(
                    status_code=404,
                    detail="Configuration file not found"
                )
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            return config
        except yaml.YAMLError as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid YAML format: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Error reading config: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error reading configuration: {str(e)}"
            )
    
    async def write_config(self, config: Dict[str, Any]) -> bool:
        """写入配置文件"""
        try:
            # 创建备份
            await self.backup_config()
            
            # 写入新配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info("Configuration updated successfully")
            return True
        except Exception as e:
            logger.error(f"Error writing config: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error writing configuration: {str(e)}"
            )
    
    async def backup_config(self) -> str:
        """备份配置文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"config_{timestamp}.yaml"
            
            if self.config_file.exists():
                import shutil
                shutil.copy2(self.config_file, backup_file)
                logger.info(f"Configuration backed up to {backup_file}")
                return str(backup_file)
            else:
                raise HTTPException(
                    status_code=404,
                    detail="Configuration file not found for backup"
                )
        except Exception as e:
            logger.error(f"Error backing up config: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error backing up configuration: {str(e)}"
            )
    
    async def list_backups(self) -> list:
        """列出所有备份文件"""
        try:
            backups = []
            for backup_file in self.backup_dir.glob("config_*.yaml"):
                stat = backup_file.stat()
                backups.append({
                    "filename": backup_file.name,
                    "path": str(backup_file),
                    "size": stat.st_size,
                    "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
                })
            
            # 按创建时间倒序排列
            backups.sort(key=lambda x: x["created"], reverse=True)
            return backups
        except Exception as e:
            logger.error(f"Error listing backups: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error listing backups: {str(e)}"
            )

class NetworkClient:
    """网络服务 API 客户端"""

    def __init__(self, base_url: str = None, secret: str = None):
        self.base_url = base_url or config.MIHOMO_API_BASE
        self.secret = secret or config.MIHOMO_SECRET
        self.headers = {"Authorization": f"Bearer {self.secret}"}

    async def request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送请求到网络服务 API"""
        url = f"{self.base_url}{endpoint}"

        try:
            timeout = aiohttp.ClientTimeout(total=config.MIHOMO_API_TIMEOUT)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.request(
                    method, url, headers=self.headers, **kwargs
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        raise HTTPException(
                            status_code=response.status,
                            detail=f"Network service API error: {error_text}"
                        )
        except aiohttp.ClientError as e:
            logger.error(f"Error connecting to network service API: {e}")
            raise HTTPException(
                status_code=503,
                detail=f"Cannot connect to network service: {str(e)}"
            )

    async def get_version(self) -> Dict[str, Any]:
        """获取版本信息"""
        return await self.request("GET", "/version")

    async def get_endpoints(self) -> Dict[str, Any]:
        """获取端点信息"""
        return await self.request("GET", "/proxies")

    async def get_traffic(self) -> Dict[str, Any]:
        """获取流量信息"""
        return await self.request("GET", "/traffic")

    async def get_connections(self) -> Dict[str, Any]:
        """获取连接信息"""
        return await self.request("GET", "/connections")

# 移除 SSL 相关功能，仅使用 HTTP 和应用层加密

# 创建管理器实例
config_manager = ConfigManager()
network_client = NetworkClient()

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# 配置管理路由
@app.get("/api/config/config.yaml")
async def get_config(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取当前配置文件"""
    try:
        config_data = await config_manager.read_config()
        return {
            "success": True,
            "data": config_data,
            "message": "Configuration retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting config: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get configuration: {str(e)}"
        )

@app.put("/api/config/config.yaml")
async def update_config(
    config_data: Dict[str, Any],
    current_user: dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """更新配置文件"""
    try:
        # 验证配置格式
        if not isinstance(config_data, dict):
            raise HTTPException(
                status_code=400,
                detail="Configuration must be a valid YAML object"
            )

        # 检查必要字段
        required_fields = ["external-controller"]
        for field in required_fields:
            if field not in config_data:
                raise HTTPException(
                    status_code=400,
                    detail=f"Missing required field: {field}"
                )

        success = await config_manager.write_config(config_data)
        if success:
            return {
                "success": True,
                "message": "Configuration updated successfully"
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to update configuration"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating config: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update configuration: {str(e)}"
        )

@app.post("/api/config/backup")
async def backup_config(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """备份当前配置文件"""
    try:
        backup_path = await config_manager.backup_config()
        return {
            "success": True,
            "data": {"backup_path": backup_path},
            "message": "Configuration backed up successfully"
        }
    except Exception as e:
        logger.error(f"Error backing up config: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to backup configuration: {str(e)}"
        )

# 网络服务数据路由
@app.get("/api/service/version")
async def get_service_version(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取服务版本信息"""
    try:
        version_info = await network_client.get_version()
        return {
            "success": True,
            "data": version_info,
            "message": "Version information retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting version: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to get version information: {str(e)}"
        )

@app.get("/api/service/endpoints")
async def get_service_endpoints(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取端点信息"""
    try:
        endpoints_info = await network_client.get_endpoints()
        return {
            "success": True,
            "data": endpoints_info,
            "message": "Endpoint information retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting endpoints: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to get endpoint information: {str(e)}"
        )

@app.get("/api/service/traffic")
async def get_service_traffic(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取流量统计信息"""
    try:
        traffic_info = await network_client.get_traffic()
        return {
            "success": True,
            "data": traffic_info,
            "message": "Traffic information retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting traffic: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to get traffic information: {str(e)}"
        )

@app.get("/api/config/backups")
async def list_backups(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """列出所有配置备份"""
    try:
        backups = await config_manager.list_backups()
        return {
            "success": True,
            "data": backups,
            "message": f"Found {len(backups)} backup(s)"
        }
    except Exception as e:
        logger.error(f"Error listing backups: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list backups: {str(e)}"
        )

@app.get("/api/service/connections")
async def get_service_connections(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取连接信息"""
    try:
        connections_info = await network_client.get_connections()
        return {
            "success": True,
            "data": connections_info,
            "message": "Connection information retrieved successfully"
        }
    except Exception as e:
        logger.error(f"Error getting connections: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to get connection information: {str(e)}"
        )

@app.get("/api/service/status")
async def get_service_status(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取网络服务状态"""
    try:
        # 并发获取多个信息
        tasks = [
            network_client.get_version(),
            network_client.get_traffic(),
        ]

        try:
            version_info, traffic_info = await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Error gathering status info: {e}")
            raise HTTPException(
                status_code=503,
                detail="Network service is not available"
            )

        # 检查是否有异常
        if isinstance(version_info, Exception):
            raise HTTPException(
                status_code=503,
                detail="Network service is not responding"
            )

        status_data = {
            "service_status": "running",
            "version": version_info,
            "traffic": traffic_info if not isinstance(traffic_info, Exception) else None,
            "timestamp": datetime.now().isoformat()
        }

        return {
            "success": True,
            "data": status_data,
            "message": "Status information retrieved successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Failed to get status information: {str(e)}"
        )

# 认证端点
@app.post("/api/auth/token")
async def login(credentials: dict):
    """用户登录获取令牌"""
    username = credentials.get("username")
    password = credentials.get("password")

    if not username or not password:
        raise HTTPException(
            status_code=400,
            detail="Username and password are required"
        )

    token = auth_manager.generate_token(username, password)
    if not token:
        raise HTTPException(
            status_code=401,
            detail="Invalid credentials"
        )

    return {"access_token": token, "token_type": "bearer"}

async def start_server():
    """启动HTTP服务器（使用应用层加密保证安全）"""
    server_config = uvicorn.Config(
        "main:app",
        host=config.API_HOST,
        port=config.API_PORT,
        log_level=config.LOG_LEVEL.lower(),
        access_log=True,
        use_colors=False,
        server_header=False,
        date_header=False,
        loop="asyncio",
        timeout_keep_alive=config.KEEPALIVE_TIMEOUT,
    )
    server = uvicorn.Server(server_config)
    logger.info(f"启动网络管理服务器: http://{config.API_HOST}:{config.API_PORT}")
    logger.info("使用应用层加密保证数据传输安全")
    await server.serve()

if __name__ == "__main__":
    # 启动网络管理服务器
    logger.info("启动网络管理服务器")
    logger.info("使用应用层加密替代 SSL/TLS")
    asyncio.run(start_server())
