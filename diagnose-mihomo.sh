#!/bin/bash

# mihomo 诊断脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=== mihomo 容器诊断 ==="

# 1. 检查容器状态
log_info "检查容器状态..."
if docker ps -f name=zfmi --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q zfmi; then
    log_success "容器正在运行"
    docker ps -f name=zfmi --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    log_error "容器未运行"
    exit 1
fi

echo ""

# 2. 检查进程状态
log_info "检查容器内进程..."
docker exec zfmi ps aux | head -20

echo ""

# 3. 检查端口监听
log_info "检查端口监听..."
docker exec zfmi netstat -tlnp 2>/dev/null | grep -E "(10801|11024|8888)" || log_warning "未发现相关端口监听"

echo ""

# 4. 检查mihomo进程详情
log_info "检查mihomo进程详情..."
if docker exec zfmi pgrep -f mihomo >/dev/null 2>&1; then
    log_success "mihomo进程存在"
    docker exec zfmi pgrep -af mihomo
else
    log_error "mihomo进程不存在"
fi

echo ""

# 5. 检查supervisor状态
log_info "检查supervisor状态..."
docker exec zfmi supervisorctl status || log_warning "无法获取supervisor状态"

echo ""

# 6. 测试API连接
log_info "测试API连接..."

# 测试健康检查API
if curl -s --connect-timeout 5 http://localhost:8888/health | grep -q "ok"; then
    log_success "Python API 正常"
else
    log_warning "Python API 异常"
fi

# 测试mihomo API
if curl -s --connect-timeout 5 -H "Authorization: Bearer jhxnb666" http://localhost:11024/version >/dev/null 2>&1; then
    log_success "mihomo API 正常"
    curl -s -H "Authorization: Bearer jhxnb666" http://localhost:11024/version | head -5
else
    log_warning "mihomo API 异常"
fi

echo ""

# 7. 检查配置文件
log_info "检查配置文件..."
if docker exec zfmi test -f /etc/mihomo/config.yaml; then
    log_success "配置文件存在"
    log_info "配置文件大小: $(docker exec zfmi stat -c%s /etc/mihomo/config.yaml) bytes"
    
    # 检查TUN配置
    if docker exec zfmi grep -q "enable: true" /etc/mihomo/config.yaml 2>/dev/null; then
        log_info "TUN模式已启用"
    else
        log_warning "TUN模式未启用"
    fi
else
    log_error "配置文件不存在"
fi

echo ""

# 8. 检查TUN设备
log_info "检查TUN设备..."
if docker exec zfmi test -c /dev/net/tun; then
    log_success "TUN设备存在"
    docker exec zfmi ls -la /dev/net/tun
else
    log_error "TUN设备不存在"
fi

echo ""

# 9. 检查网络权限
log_info "检查网络权限..."
if docker exec zfmi capsh --print 2>/dev/null | grep -q "cap_net_admin"; then
    log_success "NET_ADMIN权限正常"
else
    log_warning "缺少NET_ADMIN权限"
fi

echo ""

# 10. 检查mihomo日志
log_info "最近的mihomo日志..."
docker exec zfmi tail -20 /var/log/supervisor/mihomo-stdout.log 2>/dev/null || log_warning "无法读取mihomo日志"

echo ""

# 11. 手动测试mihomo
log_info "手动测试mihomo启动..."
log_info "尝试直接运行mihomo命令..."
docker exec zfmi timeout 10s /usr/local/bin/mihomo -d /etc/mihomo -f /etc/mihomo/config.yaml 2>&1 | head -10 || log_warning "mihomo手动启动测试完成"

echo ""

# 12. 检查网络连接
log_info "检查网络连接..."
if docker exec zfmi ping -c 2 8.8.8.8 >/dev/null 2>&1; then
    log_success "外网连接正常"
else
    log_warning "外网连接异常"
fi

echo ""

# 13. 检查DNS解析
log_info "检查DNS解析..."
if docker exec zfmi nslookup google.com >/dev/null 2>&1; then
    log_success "DNS解析正常"
else
    log_warning "DNS解析异常"
fi

echo ""

log_info "=== 诊断完成 ==="
log_info "如果mihomo API无法访问，可能的原因："
log_info "1. mihomo进程启动失败或卡住"
log_info "2. 配置文件有问题"
log_info "3. 网络权限不足"
log_info "4. TUN设备问题"
log_info "5. 下载MMDB文件失败"

echo ""
log_info "建议操作："
log_info "1. 检查mihomo进程是否真正启动: docker exec zfmi pgrep -af mihomo"
log_info "2. 查看详细日志: docker logs -f zfmi"
log_info "3. 重启容器: docker restart zfmi"
log_info "4. 手动进入容器调试: docker exec -it zfmi /bin/sh"
