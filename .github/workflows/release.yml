name: Release Docker Images

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag'
        required: true
        type: string
      mihomo_version:
        description: 'mihomo version'
        required: true
        default: 'v1.19.11'
        type: string

env:
  REGISTRY: docker.io
  IMAGE_NAME: zfmi

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Set version variables
      id: vars
      run: |
        if [ "${{ github.event_name }}" = "release" ]; then
          echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          echo "MIHOMO_VERSION=${{ github.event.inputs.mihomo_version || 'v1.19.11' }}" >> $GITHUB_OUTPUT
        else
          echo "VERSION=${{ github.event.inputs.tag }}" >> $GITHUB_OUTPUT
          echo "MIHOMO_VERSION=${{ github.event.inputs.mihomo_version }}" >> $GITHUB_OUTPUT
        fi

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ secrets.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}:${{ steps.vars.outputs.VERSION }}
          ${{ env.REGISTRY }}/${{ secrets.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}:latest
        build-args: |
          MIHOMO_VERSION=${{ steps.vars.outputs.MIHOMO_VERSION }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Update Docker Hub description
      uses: peter-evans/dockerhub-description@v4
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        repository: ${{ secrets.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}
        readme-filepath: ./README.md
      continue-on-error: true
