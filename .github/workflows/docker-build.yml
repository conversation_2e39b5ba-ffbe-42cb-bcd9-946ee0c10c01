name: <PERSON><PERSON> and Push Docker Images

on:
  push:
    branches:
      - main
      - master
    tags:
      - 'v*'
  pull_request:
    branches:
      - main
      - master
  workflow_dispatch:
    inputs:
      mihomo_version:
        description: 'mihomo version to build'
        required: false
        default: 'v1.19.11'
        type: string
      push_to_registry:
        description: 'Push to Docker Hub'
        required: false
        default: true
        type: boolean

env:
  REGISTRY: docker.io
  IMAGE_NAME: zfmi
  MIHOMO_VERSION: ${{ github.event.inputs.mihomo_version || 'v1.19.11' }}

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      id-token: write
      attestations: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        platforms: linux/amd64,linux/arm64

    - name: Log in to Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ secrets.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          type=raw,value=latest,enable={{is_default_branch}}
          type=raw,value=${{ env.MIHOMO_VERSION }},enable=true

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' && (github.event.inputs.push_to_registry != 'false') }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        build-args: |
          MIHOMO_VERSION=${{ env.MIHOMO_VERSION }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Image digest
      if: github.event_name != 'pull_request'
      run: echo "Image pushed with digest ${{ steps.build.outputs.digest }}"
